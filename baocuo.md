                                                                                                    android.content.pm.PackageManager$NameNotFoundException: No module info for package: com.android.wifi
                                                                                                    	at android.app.ApplicationPackageManager.getModuleInfo(ApplicationPackageManager.java:1187)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.getWifiStackVersion(WifiHealthMonitor.java:366)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.extractCurrentSoftwareBuildInfo(WifiHealthMonitor.java:587)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootSwBuildCheck(WifiHealthMonitor.java:522)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.postBootDetectionHandler(WifiHealthMonitor.java:513)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor.-$$Nest$mpostBootDetectionHandler(Unknown Source:0)
                                                                                                    	at com.android.server.wifi.WifiHealthMonitor$2.onAlarm(WifiHealthMonitor.java:190)
                                                                                                    	at android.app.AlarmManager$ListenerWrapper.run(AlarmManager.java:357)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 20:37:09.952  2127-2597  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.080  4810-4810  DEBUG                   crash_dump64                         E  failed to read process info: failed to open /proc/4639
2025-05-25 20:37:10.098  1831-2236  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@cab8acfb, EventCode: GET_STORAGE_INFO [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzp: 29514: Storage info not created for GMS or Play Store.
                                                                                                    	at bndq.b(:com.google.android.gms@242632114@24.26.32 (230800-650348549):807)
                                                                                                    	at bncl.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):13)
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1)
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:10.166  2403-4681  JavaBinder              com.google.android.gms               E  !!! FAILED BINDER TRANSACTION !!!  (parcel size = 1056768)
2025-05-25 20:37:10.168  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.170  2127-2597  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.170  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.329  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.425  2127-2596  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.431  2127-2596  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.433  2127-2596  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.436  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.438  2127-2596  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.439  2127-2597  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.439  2127-2596  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.452   233-233   tombstoned              tombstoned                           E  Tombstone written to: tombstone_21
2025-05-25 20:37:10.468   321-396   BpfHandler              netd                                 E  Unsupported protocol: 1
2025-05-25 20:37:10.476   615-733   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 20:37:10.477   615-733   NativeTombstoneManager  system_server                        E  Tombstone's UID (1001002) not an app, ignoring
2025-05-25 20:37:10.599   615-750   BluetoothManagerService system_server                        E  getState() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.getState(IBluetooth.java:1536)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousGetState(BluetoothManagerService.java:892)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2908)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2895)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mwaitForState(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2370)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 20:37:10.599   615-750   BluetoothManagerService system_server                        E  waitForState [12] time out
2025-05-25 20:37:10.599   615-750   BluetoothManagerService system_server                        E  Unable to call disable() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.disable(IBluetooth.java:1563)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousDisable(BluetoothManagerService.java:856)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.handleDisable(BluetoothManagerService.java:2713)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mhandleDisable(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2371)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 20:37:10.599   615-750   BluetoothManagerService system_server                        E  getState() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.getState(IBluetooth.java:1536)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousGetState(BluetoothManagerService.java:892)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2908)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.waitForState(BluetoothManagerService.java:2895)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mwaitForState(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2372)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 20:37:10.599   615-750   BluetoothManagerService system_server                        E  waitForState [10, 11, 16, 14, 15, 13] time out
2025-05-25 20:37:10.630  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.632  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.650  2127-2575  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:10.668   615-750   BluetoothManagerService system_server                        E  Unable to call enable() (Ask Gemini)
                                                                                                    android.os.DeadObjectException
                                                                                                    	at android.os.BinderProxy.transactNative(Native Method)
                                                                                                    	at android.os.BinderProxy.transact(BinderProxy.java:584)
                                                                                                    	at android.bluetooth.IBluetooth$Stub$Proxy.enable(IBluetooth.java:1550)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.synchronousEnable(BluetoothManagerService.java:865)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.handleEnable(BluetoothManagerService.java:2683)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService.-$$Nest$mhandleEnable(Unknown Source:0)
                                                                                                    	at com.android.server.bluetooth.BluetoothManagerService$BluetoothHandler.handleMessage(BluetoothManagerService.java:2489)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.os.HandlerThread.run(HandlerThread.java:67)
2025-05-25 20:37:10.668   615-750   BluetoothManagerService system_server                        E  MESSAGE_BLUETOOTH_SERVICE_DISCONNECTED(1)
2025-05-25 20:37:10.670   615-615   BluetoothAdapter        system_server                        E  java.lang.RuntimeException: android.os.DeadObjectException (Ask Gemini)
                                                                                                    java.lang.Throwable
                                                                                                    	at android.bluetooth.BluetoothAdapter.getStateInternal(BluetoothAdapter.java:1320)
                                                                                                    	at android.bluetooth.BluetoothAdapter.getLeState(BluetoothAdapter.java:1378)
                                                                                                    	at android.bluetooth.BluetoothAdapter.isLeEnabled(BluetoothAdapter.java:1157)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner.checkBleState(BleCompanionDeviceScanner.java:157)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner.-$$Nest$mcheckBleState(Unknown Source:0)
                                                                                                    	at com.android.server.companion.presence.BleCompanionDeviceScanner$1.onReceive(BleCompanionDeviceScanner.java:312)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args.lambda$getRunnable$0$android-app-LoadedApk$ReceiverDispatcher$Args(LoadedApk.java:1790)
                                                                                                    	at android.app.LoadedApk$ReceiverDispatcher$Args$$ExternalSyntheticLambda0.run(Unknown Source:2)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at com.android.server.SystemServer.run(SystemServer.java:962)
                                                                                                    	at com.android.server.SystemServer.main(SystemServer.java:647)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:914)
2025-05-25 20:37:10.936  4778-4778  GooglePartnerSetup      com.google.android.partnersetup      E  Phenotype client.register: true
2025-05-25 20:37:10.986  2127-2540  GenericAppFlowLogger    com.google.android.carassistant      E  Event logging failed. (Ask Gemini)
                                                                                                    txb: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:31)
                                                                                                    	at tgt.apply(PG:209)
                                                                                                    	at uzg.apply(PG:13)
                                                                                                    	at xxp.d(PG:3)
                                                                                                    	at xxq.run(PG:42)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xzs.fG(PG:7)
                                                                                                    	at xxq.f(PG:10)
                                                                                                    	at wab.bj(PG:5)
                                                                                                    	at ubg.t(PG:22)
                                                                                                    	at twg.c(PG:3)
                                                                                                    	at txf.c(PG:9)
                                                                                                    	at sqf.d(PG:61)
                                                                                                    	at sqi.eF(PG:451)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at ujr.run(PG:955)
                                                                                                    	at acbp.run(PG:27)
                                                                                                    	at lvo.run(PG:3)
                                                                                                    	at xrk.run(PG:50)
                                                                                                    	at lsy.run(PG:512)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    	at lwy.run(PG:74)
                                                                                                    Caused by: java.lang.IllegalArgumentException
                                                                                                    	at ubg.l(PG:24)
                                                                                                    	at tgt.apply(PG:209) 
                                                                                                    	at uzg.apply(PG:13) 
                                                                                                    	at xxp.d(PG:3) 
                                                                                                    	at xxq.run(PG:42) 
                                                                                                    	at xyq.execute(PG:1) 
                                                                                                    	at xzs.fG(PG:7) 
                                                                                                    	at xxq.f(PG:10) 
                                                                                                    	at wab.bj(PG:5) 
                                                                                                    	at ubg.t(PG:22) 
                                                                                                    	at twg.c(PG:3) 
                                                                                                    	at txf.c(PG:9) 
                                                                                                    	at sqf.d(PG:61) 
                                                                                                    	at sqi.eF(PG:451) 
                                                                                                    	at acvf.fW(PG:12) 
                                                                                                    	at aday.run(PG:109) 
                                                                                                    	at ujr.run(PG:955) 
                                                                                                    	at acbp.run(PG:27) 
                                                                                                    	at lvo.run(PG:3) 
                                                                                                    	at xrk.run(PG:50) 
                                                                                                    	at lsy.run(PG:512) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    	at lwy.run(PG:74) 
2025-05-25 20:37:12.401  3475-3650  Finsky                  com.android.vending                  E  [245] gjj.accept(91): GMS compliance query failed.
                                                                                                    com.google.common.util.concurrent.TimeoutFuture$TimeoutFutureException: Timed out: ablb@7ed5386[status=PENDING, setFuture=[dqd@f239447[status=PENDING, info=[tag=[vxg@f3df474]]]]]
2025-05-25 20:37:12.923  1831-4691  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.lockbox failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@242632114@24.26.32 (230800-650348549):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):4)
                                                                                                    	at com.google.android.gms.lockbox.LockboxIntentOperation.onHandleIntent(:com.google.android.gms@242632114@24.26.32 (230800-650348549):48)
                                                                                                    	at com.google.android.chimera.IntentOperation.onHandleIntent(:com.google.android.gms@242632114@24.26.32 (230800-650348549):2)
                                                                                                    	at aaef.onHandleIntent(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at lnq.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):70)
                                                                                                    	at lnp.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):152)
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:13.080   615-750   BluetoothManagerService system_server                        E  Reach maximum retry to restart Bluetooth!
2025-05-25 20:37:18.401  1494-2752  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 20:37:20.145  1072-1257  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-25 20:37:20.391  3182-3895  OpenGLRenderer          com...d.apps.automotive.inputmethod  E  Unable to match the desired swap behavior.
2025-05-25 20:37:24.352  1844-2017  WorkSourceUtil          com.google.android.gms               E  Could not find package: com.google.android.gms.westworld
2025-05-25 20:37:25.325  3079-3263  Finsky                  com.android.vending                  E  [227] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:25.325  3079-3263  Finsky                  com.android.vending                  E  [227] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:25.326  3079-3263  Finsky                  com.android.vending                  E  [227] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:25.356  3079-3295  Finsky                  com.android.vending                  E  [238] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:25.637  3182-3795  native                  com...d.apps.automotive.inputmethod  E  E0000 00:00:1748176645.637253    3795 scoped-file.cc:107] open() failed for /data/user/10/com.google.android.apps.automotive.inputmethod/files/personal/adapt_state.en.dat: No such file or directory [2]
2025-05-25 20:37:26.085  2787-2787  SpannableStringBuilder  com.example.aimusicplayer            E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-05-25 20:37:26.085  2787-2787  SpannableStringBuilder  com.example.aimusicplayer            E  SPAN_EXCLUSIVE_EXCLUSIVE spans cannot have a zero length
2025-05-25 20:37:29.433  3475-3645  Finsky                  com.android.vending                  E  [242] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:29.434  3475-3645  Finsky                  com.android.vending                  E  [242] rcz.WA(261): [PLUS] Failed to trigger NOW_EXCLUSIVE urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:29.434  3475-3645  Finsky                  com.android.vending                  E  [242] gmr.WA(29): [DeviceConfig] failed to update device attribute payloads (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:29.452  3475-3660  Finsky                  com.android.vending                  E  [255] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:31.670  1072-1257  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-25 20:37:33.653  3079-3295  Finsky                  com.android.vending                  E  [238] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:35.024   615-784   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-25 20:37:37.645  2787-2971  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 20:37:37.719  3182-3895  OpenGLRenderer          com...d.apps.automotive.inputmethod  E  Unable to match the desired swap behavior.
2025-05-25 20:37:38.074  3475-3660  Finsky                  com.android.vending                  E  [255] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:39.419  1072-1257  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-25 20:37:41.904  2787-2971  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 20:37:41.958  3182-3895  OpenGLRenderer          com...d.apps.automotive.inputmethod  E  Unable to match the desired swap behavior.
2025-05-25 20:37:43.896  1072-1257  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-25 20:37:44.333  5094-5094  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 20:37:44.765  2787-2971  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-25 20:37:44.863  3079-3274  Finsky                  com.android.vending                  E  [232] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:44.863  3079-3274  Finsky                  com.android.vending                  E  [232] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:44.865  3079-3274  Finsky                  com.android.vending                  E  [232] obb.a(333): SCH: Job 37-34 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:44.903  3182-3895  OpenGLRenderer          com...d.apps.automotive.inputmethod  E  Unable to match the desired swap behavior.
2025-05-25 20:37:48.720  5159-5159  ng:quick_launch         com.android.vending                  E  Not starting debugger since process cannot load the jdwp agent.
2025-05-25 20:37:49.166  3475-3651  Finsky                  com.android.vending                  E  [246] iuw.a(52): Unexpected android-id = 0
2025-05-25 20:37:49.166  3475-3651  Finsky                  com.android.vending                  E  [246] rcz.WA(261): [PLUS] Failed to trigger NOW urgency sync (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:49.168  3475-3651  Finsky                  com.android.vending                  E  [246] obb.a(333): SCH: Job 37-33 threw exception (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: Cannot sync with 0 Android ID
                                                                                                    	at suw.a(PG:71)
                                                                                                    	at rak.a(PG:144)
                                                                                                    	at ablb.d(PG:3)
                                                                                                    	at abld.run(PG:42)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at kiq.run(PG:324)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:37:56.536  1072-1257  OpenGLRenderer          com.android.systemui                 E  Unable to match the desired swap behavior.
2025-05-25 20:37:57.702  1494-3733  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-25 20:38:03.120  1844-2620  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
2025-05-25 20:38:05.661  1494-1872  bnan                    com.google.android.gms.persistent    E  Phenotype registration failed [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bnan.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):68)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):47)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-25 20:38:10.759  2403-2898  WakeLock                com.google.android.gms               E  IntentOp:.chimera.container.InitConfigOperation ** IS FORCE-RELEASED ON TIMEOUT **
