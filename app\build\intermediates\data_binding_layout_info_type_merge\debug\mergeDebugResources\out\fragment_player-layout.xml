<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="392" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="215" endOffset="18"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="49" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="97" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="113" endOffset="46"/></Target><Target id="@+id/search_container" view="RelativeLayout"><Expressions/><location startLine="126" startOffset="12" endLine="167" endOffset="28"/></Target><Target id="@+id/search_edit_text" view="EditText"><Expressions/><location startLine="135" startOffset="16" endLine="153" endOffset="47"/></Target><Target id="@+id/search_button" view="ImageView"><Expressions/><location startLine="156" startOffset="16" endLine="166" endOffset="46"/></Target><Target id="@+id/search_suggestions_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="170" startOffset="12" endLine="179" endOffset="56"/></Target><Target id="@+id/search_results_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="182" startOffset="12" endLine="191" endOffset="41"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="194" startOffset="12" endLine="204" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="207" startOffset="12" endLine="213" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="218" startOffset="4" endLine="228" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="231" startOffset="4" endLine="391" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="251" startOffset="12" endLine="259" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="261" startOffset="12" endLine="271" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="273" startOffset="12" endLine="281" endOffset="38"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="294" startOffset="12" endLine="305" endOffset="70"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="308" startOffset="12" endLine="319" endOffset="70"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="322" startOffset="12" endLine="333" endOffset="70"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="336" startOffset="12" endLine="347" endOffset="79"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="350" startOffset="12" endLine="361" endOffset="70"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="364" startOffset="12" endLine="375" endOffset="70"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="378" startOffset="12" endLine="389" endOffset="70"/></Target></Targets></Layout>