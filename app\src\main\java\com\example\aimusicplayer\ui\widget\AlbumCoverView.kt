package com.example.aimusicplayer.ui.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ValueAnimator
import android.animation.ValueAnimator.AnimatorUpdateListener
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Point
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import androidx.core.content.res.ResourcesCompat
import com.example.aimusicplayer.BuildConfig
import com.example.aimusicplayer.R
import com.example.aimusicplayer.utils.ImageUtils

/**
 * 高仿云音乐黑胶唱片专辑封面视图
 * 参考: https://juejin.cn/post/6844903759202484232
 * 实现黑胶唱片旋转动画效果，专辑封面在黑胶里面并贴合
 */
class AlbumCoverView @JvmOverloads constructor(
    context: Context?,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // 黑胶唱片背景
    private var discBitmap: Bitmap? = null
    private val discMatrix by lazy { Matrix() }
    private val discStartPoint by lazy { Point() } // 图片起始坐标
    private val discCenterPoint by lazy { Point() } // 旋转中心坐标
    private var discRotation = 0.0f

    // 唱针
    private var needleBitmap: Bitmap? = null
    private val needleMatrix by lazy { Matrix() }
    private val needleStartPoint by lazy { Point() }
    private val needleCenterPoint by lazy { Point() }
    private var needleRotation = NEEDLE_ROTATION_PLAY

    // 专辑封面
    private var coverBitmap: Bitmap? = null
    private val coverMatrix by lazy { Matrix() }
    private val coverStartPoint by lazy { Point() }
    private val coverCenterPoint by lazy { Point() }
    private var coverSize = 0

    // 绘制相关
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val coverRect = RectF()

    // 动画相关
    private val coverBorder: Drawable by lazy {
        ResourcesCompat.getDrawable(resources, R.drawable.bg_playing_cover_border, null)!!
    }

    private val rotationAnimator by lazy {
        ValueAnimator.ofFloat(0f, 360f).apply {
            duration = 20000
            repeatCount = ValueAnimator.INFINITE
            interpolator = LinearInterpolator()
            addUpdateListener(rotationUpdateListener)
            // 优化动画性能
            setFloatValues(0f, 360f)
        }
    }
    private val playAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PAUSE, NEEDLE_ROTATION_PLAY).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }
    private val pauseAnimator by lazy {
        ValueAnimator.ofFloat(NEEDLE_ROTATION_PLAY, NEEDLE_ROTATION_PAUSE).apply {
            duration = 300
            addUpdateListener(animationUpdateListener)
        }
    }

    // 状态
    private var isPlaying = false
    private var lastRotation = 0f // 记录上次旋转角度，用于恢复旋转

    init {
        // 初始化画笔
        circlePaint.color = 0xFF111111.toInt() // 黑色
        circlePaint.style = Paint.Style.FILL

        // 设置混合模式，用于绘制圆形封面
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // 初始化位图资源
        initBitmaps()
    }

    /**
     * 初始化位图资源
     * 安全地从资源创建Bitmap，支持Vector Drawable
     */
    private fun initBitmaps() {
        try {
            // 初始化黑胶唱片背景
            discBitmap = createBitmapFromResource(R.drawable.bg_playing_disc)

            // 初始化唱针 - 使用新的唱臂PNG图片并处理透明背景
            needleBitmap = loadAndProcessTonearmImage()

        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "初始化位图资源失败", e)
            // 创建默认位图以防止崩溃
            createDefaultBitmaps()
        }
    }

    /**
     * 加载并处理唱臂图片，确保透明背景
     * 根据网易云音乐的设计，唱臂应该有透明背景且不受黑胶区域限制
     */
    private fun loadAndProcessTonearmImage(): Bitmap? {
        return try {
            // 首先尝试加载PNG图片
            val inputStream = context.resources.openRawResource(R.drawable.ic_tonearm_new)
            val originalBitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()

            if (originalBitmap != null) {
                // 处理白色背景为透明背景
                processWhiteBackgroundToTransparent(originalBitmap)
            } else {
                // 如果PNG加载失败，尝试Vector Drawable
                createBitmapFromResource(R.drawable.ic_tonearm_new)
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "加载唱臂图片失败", e)
            // 返回默认唱臂
            createDefaultNeedleBitmap()
        }
    }

    /**
     * 将白色背景转换为透明背景
     * 适配用户提供的唱臂图片
     */
    private fun processWhiteBackgroundToTransparent(originalBitmap: Bitmap): Bitmap {
        val width = originalBitmap.width
        val height = originalBitmap.height
        val processedBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)

        val pixels = IntArray(width * height)
        originalBitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // 将白色和接近白色的像素设为透明
        for (i in pixels.indices) {
            val pixel = pixels[i]
            val red = (pixel shr 16) and 0xFF
            val green = (pixel shr 8) and 0xFF
            val blue = pixel and 0xFF
            val alpha = (pixel shr 24) and 0xFF

            // 如果是白色或接近白色（RGB值都大于240），设为透明
            if (red > 240 && green > 240 && blue > 240) {
                pixels[i] = 0x00000000 // 完全透明
            } else {
                // 保持原有颜色和透明度
                pixels[i] = pixel
            }
        }

        processedBitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        return processedBitmap
    }

    /**
     * 从资源创建Bitmap，支持Vector Drawable
     */
    private fun createBitmapFromResource(resourceId: Int): Bitmap? {
        return try {
            val drawable = ResourcesCompat.getDrawable(resources, resourceId, null)
            drawable?.let { ImageUtils.drawableToBitmap(it) }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "从资源创建Bitmap失败: $resourceId", e)
            null
        }
    }

    /**
     * 创建默认位图以防止崩溃
     */
    private fun createDefaultBitmaps() {
        try {
            // 创建默认的黑胶唱片背景（简单的圆形）
            if (discBitmap == null) {
                discBitmap = createDefaultDiscBitmap()
            }

            // 创建默认的唱针（简单的矩形）
            if (needleBitmap == null) {
                needleBitmap = createDefaultNeedleBitmap()
            }
        } catch (e: Exception) {
            android.util.Log.e("AlbumCoverView", "创建默认位图失败", e)
        }
    }

    /**
     * 创建默认的黑胶唱片背景
     */
    private fun createDefaultDiscBitmap(): Bitmap {
        val size = 200
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // 绘制黑色圆形
        paint.color = 0xFF333333.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint)

        // 绘制中心小圆
        paint.color = 0xFF666666.toInt()
        canvas.drawCircle(size / 2f, size / 2f, size / 8f, paint)

        return bitmap
    }

    /**
     * 创建默认的唱针
     */
    private fun createDefaultNeedleBitmap(): Bitmap {
        val width = 20
        val height = 100
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)

        // 绘制灰色矩形作为唱针
        paint.color = 0xFF888888.toInt()
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), paint)

        return bitmap
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            initSize()
        }
    }

    /**
     * 确定图片起始坐标与旋转中心坐标
     * 根据网易云音乐唱片机设计，唱臂不受黑胶区域限制，完整显示
     */
    private fun initSize() {
        val unit = width.coerceAtMost(height) / 10  // 调整单位，为唱臂留出更多空间

        // 1. 初始化黑胶唱片 - 居中偏左显示，为唱臂留出空间
        discBitmap?.let { disc ->
            discBitmap = ImageUtils.resizeImage(disc, unit * 6, unit * 6)
        }

        val discWidth = discBitmap?.width ?: (unit * 6)
        val discHeight = discBitmap?.height ?: (unit * 6)

        // 唱片位置：水平居中偏左，垂直居中，为唱臂预留右侧空间
        val discOffsetX = -width / 8  // 向左偏移，为唱臂留出空间
        val discOffsetY = (height - discHeight) / 2  // 垂直居中
        discStartPoint.x = (width - discWidth) / 2 + discOffsetX
        discStartPoint.y = discOffsetY
        discCenterPoint.x = discStartPoint.x + discWidth / 2
        discCenterPoint.y = discStartPoint.y + discHeight / 2

        // 2. 初始化专辑封面 - 保持与黑胶唱片的比例关系
        coverSize = (discWidth * 0.65).toInt()  // 封面直径为唱片的65%
        coverStartPoint.x = discStartPoint.x + (discWidth - coverSize) / 2
        coverStartPoint.y = discStartPoint.y + (discHeight - coverSize) / 2
        coverCenterPoint.x = discCenterPoint.x
        coverCenterPoint.y = discCenterPoint.y

        // 3. 初始化唱臂 - 根据网易云音乐设计，唱臂完整显示且不受限制
        needleBitmap?.let { needle ->
            // 保持唱臂原始比例，适当缩放以适配屏幕
            val originalRatio = needle.width.toFloat() / needle.height.toFloat()
            val needleHeight = (discWidth * 0.8).toInt()  // 唱臂高度为唱片直径的80%
            val needleWidth = (needleHeight * originalRatio).toInt()
            needleBitmap = ImageUtils.resizeImage(needle, needleWidth, needleHeight)
        }

        val needleWidth = needleBitmap?.width ?: (unit * 3)
        val needleHeight = needleBitmap?.height ?: (unit * 5)

        // 根据网易云音乐设计，唱臂位于唱片右上方
        // 唱臂基座位置：唱片右侧外部，高度在唱片上方
        needleStartPoint.x = discCenterPoint.x + (discWidth * 0.3).toInt()  // 右侧外部
        needleStartPoint.y = discStartPoint.y - (needleHeight * 0.3).toInt()  // 上方位置

        // 旋转中心：唱臂基座的旋转轴心（通常在唱臂顶部偏左）
        needleCenterPoint.x = needleStartPoint.x + (needleWidth * 0.1).toInt()  // 基座旋转轴心
        needleCenterPoint.y = needleStartPoint.y + (needleHeight * 0.15).toInt()  // 顶部位置

        Log.d("AlbumCoverView", "唱臂尺寸: ${needleWidth}x${needleHeight}")
        Log.d("AlbumCoverView", "唱臂位置: (${needleStartPoint.x}, ${needleStartPoint.y})")
        Log.d("AlbumCoverView", "旋转中心: (${needleCenterPoint.x}, ${needleCenterPoint.y})")
    }

    override fun onDraw(canvas: Canvas) {
        try {
            // 检查画布有效性
            if (width <= 0 || height <= 0) {
                return
            }

            // 保存画布状态
            val saveCount = canvas.save()

            // 1. 绘制专辑封面（圆形裁剪，在黑胶里面）
            val cover = coverBitmap
            if (cover != null && !cover.isRecycled) {
                try {
                    // 保存画布状态用于裁剪
                    val clipSaveCount = canvas.save()

                    // 创建圆形裁剪路径
                    val clipPath = Path()
                    clipPath.addCircle(
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat(),
                        coverSize / 2f,
                        Path.Direction.CW
                    )
                    canvas.clipPath(clipPath)

                    // 绘制旋转的专辑封面
                    coverMatrix.reset()
                    coverMatrix.setRotate(
                        discRotation, // 封面跟随唱片旋转
                        coverCenterPoint.x.toFloat(),
                        coverCenterPoint.y.toFloat()
                    )
                    coverMatrix.preTranslate(coverStartPoint.x.toFloat(), coverStartPoint.y.toFloat())
                    coverMatrix.preScale(
                        coverSize.toFloat() / cover.width,
                        coverSize.toFloat() / cover.height
                    )
                    canvas.drawBitmap(cover, coverMatrix, null)

                    // 恢复裁剪状态
                    canvas.restoreToCount(clipSaveCount)
                } catch (e: Exception) {
                    Log.e("AlbumCoverView", "绘制专辑封面失败", e)
                }
            }

            // 2. 绘制黑胶唱片外侧半透明边框
            try {
                discBitmap?.let { disc ->
                    coverBorder.setBounds(
                        discStartPoint.x - COVER_BORDER_WIDTH,
                        discStartPoint.y - COVER_BORDER_WIDTH,
                        discStartPoint.x + disc.width + COVER_BORDER_WIDTH,
                        discStartPoint.y + disc.height + COVER_BORDER_WIDTH
                    )
                    coverBorder.draw(canvas)
                }
            } catch (e: Exception) {
                Log.e("AlbumCoverView", "绘制边框失败", e)
            }

            // 3. 绘制黑胶唱片（在封面上面，形成遮罩效果）
            discBitmap?.let { disc ->
                if (!disc.isRecycled) {
                    try {
                        discMatrix.reset()
                        // 设置旋转中心和旋转角度，setRotate和preTranslate顺序很重要
                        // 应用旋转速度变量，实现唱片减速/加速效果
                        discMatrix.setRotate(
                            discRotation * discRotationSpeed,
                            discCenterPoint.x.toFloat(),
                            discCenterPoint.y.toFloat()
                        )
                        // 设置图片起始坐标
                        discMatrix.preTranslate(discStartPoint.x.toFloat(), discStartPoint.y.toFloat())
                        canvas.drawBitmap(disc, discMatrix, null)
                    } catch (e: Exception) {
                        Log.e("AlbumCoverView", "绘制黑胶唱片失败", e)
                    }
                }
            }

            // 4. 绘制唱臂 - 确保完整显示且不受黑胶区域限制
            needleBitmap?.let { needle ->
                if (!needle.isRecycled) {
                    try {
                        // 保存画布状态，确保唱臂绘制不受其他元素影响
                        val needleSaveCount = canvas.save()

                        needleMatrix.reset()

                        // 根据网易云音乐设计调整变换顺序
                        // 1. 先平移到起始位置
                        needleMatrix.setTranslate(needleStartPoint.x.toFloat(), needleStartPoint.y.toFloat())

                        // 2. 根据播放状态旋转唱臂（以基座为中心）
                        needleMatrix.postRotate(
                            needleRotation,
                            needleCenterPoint.x.toFloat(),
                            needleCenterPoint.y.toFloat()
                        )

                        // 3. 应用缩放效果（如果有抖动或切换动画）
                        if (needleScale != 1.0f) {
                            needleMatrix.postScale(
                                needleScale, needleScale,
                                needleCenterPoint.x.toFloat(),
                                needleCenterPoint.y.toFloat()
                            )
                        }

                        // 绘制唱臂（使用透明背景处理后的图片）
                        val needlePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                            isFilterBitmap = true  // 启用图片过滤，提高显示质量
                        }
                        canvas.drawBitmap(needle, needleMatrix, needlePaint)

                        // 恢复画布状态
                        canvas.restoreToCount(needleSaveCount)

                        // 调试辅助 - 绘制旋转中心点（开发时可启用）
                        if (BuildConfig.DEBUG) {
                            val debugPaint = Paint().apply {
                                color = android.graphics.Color.RED
                                style = Paint.Style.FILL
                            }
                            canvas.drawCircle(needleCenterPoint.x.toFloat(), needleCenterPoint.y.toFloat(), 8f, debugPaint)
                        }
                    } catch (e: Exception) {
                        Log.e("AlbumCoverView", "绘制唱针失败", e)
                    }
                }
            }

            // 恢复画布状态
            canvas.restoreToCount(saveCount)
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "onDraw执行失败", e)
        }
    }

    fun initNeedle(isPlaying: Boolean) {
        needleRotation = if (isPlaying) NEEDLE_ROTATION_PLAY else NEEDLE_ROTATION_PAUSE
        invalidate()
    }

    /**
     * 设置专辑封面
     * @param bitmap 封面图片
     */
    fun setCoverBitmap(bitmap: Bitmap) {
        coverBitmap = bitmap
        invalidate()
    }

    /**
     * 开始播放动画
     */
    fun start() {
        if (isPlaying) {
            return
        }
        isPlaying = true
        rotationAnimator.start()
        playAnimator.start()
    }

    /**
     * 暂停播放动画
     */
    fun pause() {
        if (!isPlaying) {
            return
        }
        isPlaying = false
        rotationAnimator.pause()
        pauseAnimator.start()
    }

    /**
     * 重置动画状态
     */
    fun reset() {
        isPlaying = false
        discRotation = 0.0f
        rotationAnimator.cancel()
        invalidate()
    }

    // 唱臂缩放和旋转相关变量
    private var needleScale = 1.0f
    private var discRotationSpeed = 1.0f
    private var isSwitchingTrack = false

    /**
     * 切换歌曲效果 - 优化版
     * 唱针抬起后再放下的动画，增强物理感和流畅度
     */
    fun switchTrack() {
        if (isPlaying && !isSwitchingTrack) {
            // 防止动画重复触发
            isSwitchingTrack = true

            // 使用AnimatorSet处理动画序列，提高性能
            val animatorSet = android.animation.AnimatorSet()

            // 1. 唱片逐渐减速
            val discSlowDownAnimator = ValueAnimator.ofFloat(1f, 0.5f).apply {
                duration = 200
                interpolator = DecelerateInterpolator(1.2f)
                addUpdateListener { animation ->
                    // 降低更新频率，优化性能
                    val newValue = animation.animatedValue as Float
                    if (Math.abs(discRotationSpeed - newValue) > 0.05f) {
                        discRotationSpeed = newValue
                    }
                }
            }

            // 2. 唱针轻微抖动效果
            val needleShakeAnimator = ValueAnimator.ofFloat(1f, 0.98f, 1.02f, 1f).apply {
                duration = 150
                interpolator = LinearInterpolator()
                addUpdateListener { animation ->
                    val newScale = animation.animatedValue as Float
                    if (Math.abs(needleScale - newScale) > 0.01f) {
                        needleScale = newScale
                        invalidate()
                    }
                }
            }

            // 3. 唱针抬起动画 - 根据第二张图片调整动画参数
            val liftAnimator = ValueAnimator.ofFloat(needleRotation, NEEDLE_ROTATION_PAUSE).apply {
                duration = 300  // 增加时间，使动作更加平滑
                interpolator = AccelerateInterpolator(0.8f)  // 调整加速度
                addUpdateListener { animation ->
                    val newRotation = animation.animatedValue as Float
                    // 每1度旋转就更新一次视图，确保动画流畅
                    if (Math.abs(needleRotation - newRotation) > 1.0f) {
                        needleRotation = newRotation
                        invalidate()
                    } else {
                        needleRotation = newRotation
                    }
                }
            }

            // 4. 唱针放下动画 - 适配第二张图片的效果
            val dropAnimator = ValueAnimator.ofFloat(NEEDLE_ROTATION_PAUSE, NEEDLE_ROTATION_PLAY).apply {
                duration = 350  // 放下时间更长，更加自然
                interpolator = DecelerateInterpolator(1.5f)  // 调整减速度使动画更自然
                addUpdateListener { animation ->
                    val newRotation = animation.animatedValue as Float
                    // 每1度旋转就更新一次视图，确保动画流畅
                    if (Math.abs(needleRotation - newRotation) > 1.0f) {
                        needleRotation = newRotation
                        invalidate()
                    } else {
                        needleRotation = newRotation
                    }
                }
            }

            // 5. 唱片恢复加速
            val discSpeedUpAnimator = ValueAnimator.ofFloat(0.5f, 1f).apply {
                duration = 400
                interpolator = AccelerateInterpolator(0.8f)
                addUpdateListener { animation ->
                    val newValue = animation.animatedValue as Float
                    if (Math.abs(discRotationSpeed - newValue) > 0.05f) {
                        discRotationSpeed = newValue
                    }
                }
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 恢复正常状态
                        discRotationSpeed = 1.0f
                        needleScale = 1.0f
                        isSwitchingTrack = false
                    }
                })
            }

            // 先暂停旋转
            isPlaying = false
            rotationAnimator.pause()

            // 构建动画序列
            animatorSet.play(discSlowDownAnimator).with(needleShakeAnimator)
            animatorSet.play(liftAnimator).after(needleShakeAnimator)

            // 设置动画完成回调
            animatorSet.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    // 使用Handler处理延迟，避免阻塞主线程
                    postDelayed({
                        // 创建下半部分动画序列
                        val secondAnimatorSet = android.animation.AnimatorSet()
                        secondAnimatorSet.play(dropAnimator).before(discSpeedUpAnimator)
                        secondAnimatorSet.addListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationStart(animation: Animator) {
                                // 唱针开始放下时，唱片可以开始转动
                                isPlaying = true
                                rotationAnimator.resume()
                            }
                        })
                        secondAnimatorSet.start()
                    }, 200) // 减少延迟时间提高响应性
                }
            })

            // 启动动画
            animatorSet.start()
        }
    }

    /**
     * 唱片旋转动画更新监听器
     * 优化性能，减少不必要的重绘，引入批处理更新机制
     */
    private val rotationUpdateListener = AnimatorUpdateListener { animation ->
        try {
            val newRotation = animation.animatedValue as Float
            val delta = Math.abs(newRotation - discRotation)

            // 更新角度值
            discRotation = newRotation

            // 降低重绘频率，提高性能
            // 正常播放时每6度更新一次视图
            // 切换歌曲时（速度变化时）每3度更新一次，增强流畅度
            val updateThreshold = if (discRotationSpeed == 1.0f) 6f else 3f

            if (delta > updateThreshold || delta < 0.1f) { // 跳过中间帧，只在关键帧重绘
                invalidate()
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "旋转动画更新失败", e)
        }
    }

    /**
     * 唱臂动画更新监听器
     * 优化批处理更新以提高性能，使用硬件加速提高动画流畅度
     */
    private val animationUpdateListener = AnimatorUpdateListener { animation ->
        try {
            val newRotation = animation.animatedValue as Float
            val delta = Math.abs(newRotation - needleRotation)

            // 更新角度值
            needleRotation = newRotation

            // 唱针动画更新策略
            // 动画开始和结束部分使用更高频率更新，中间部分可以降低频率
            // 0.5度变化重绘，足够流畅且性能好
            if (delta > 0.5f ||
                needleRotation < NEEDLE_ROTATION_PLAY + 2f ||
                needleRotation > NEEDLE_ROTATION_PAUSE - 2f) {
                // 使用硬件加速层优化唱臂动画
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    setLayerType(LAYER_TYPE_HARDWARE, null)
                    postDelayed({
                        // 动画完成后恢复正常渲染以节省内存
                        if (isAttachedToWindow) {
                            setLayerType(LAYER_TYPE_NONE, null)
                        }
                    }, 300)
                }
                invalidate()
            }
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "唱针动画更新失败", e)
        }
    }

    /**
     * 设置硬件加速和性能优化（优化版）
     * 提高动画流畅度，解决OpenGL渲染问题
     */
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        try {
            // 智能选择渲染模式，避免OpenGL问题
            if (isHardwareAccelerationSupported()) {
                setLayerType(LAYER_TYPE_HARDWARE, null)
                Log.d("AlbumCoverView", "硬件加速已启用")
            } else {
                setLayerType(LAYER_TYPE_SOFTWARE, null)
                Log.d("AlbumCoverView", "使用软件渲染模式")
            }

            // 优化视图属性
            setWillNotDraw(false) // 确保onDraw会被调用

        } catch (e: Exception) {
            Log.e("AlbumCoverView", "渲染模式设置失败，使用默认模式", e)
            // 如果设置失败，使用默认模式
            setLayerType(LAYER_TYPE_NONE, null)
        }
    }

    /**
     * 检查硬件加速支持情况
     */
    private fun isHardwareAccelerationSupported(): Boolean {
        return try {
            // 检查当前设备是否支持硬件加速
            val activity = context as? android.app.Activity
            activity?.window?.attributes?.flags?.and(
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            ) != 0
        } catch (e: Exception) {
            Log.w("AlbumCoverView", "无法检查硬件加速支持", e)
            false
        }
    }

    /**
     * 清理资源，防止内存泄漏
     */
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        try {
            // 停止所有动画
            rotationAnimator.cancel()
            pauseAnimator.cancel()

            // 清理位图资源
            discBitmap?.let {
                if (!it.isRecycled) {
                    // 不要手动回收，让GC处理
                    Log.d("AlbumCoverView", "位图资源已标记清理")
                }
            }

            Log.d("AlbumCoverView", "视图资源已清理")
        } catch (e: Exception) {
            Log.e("AlbumCoverView", "清理资源时发生错误", e)
        }
    }



    companion object {
        // 根据网易云音乐唱片机设计调整角度
        private const val NEEDLE_ROTATION_PLAY = 0.0f     // 播放时唱针水平接触唱片
        private const val NEEDLE_ROTATION_PAUSE = -25.0f  // 暂停时唱针抬起

        private const val COVER_BORDER_WIDTH = 6
    }
}
